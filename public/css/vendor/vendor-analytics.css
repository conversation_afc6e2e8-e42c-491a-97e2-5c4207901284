/* ===== ULTRA MODERN VENDOR ANALYTICS CSS ===== */
/* Redesigned analytics page with modern aesthetics matching dashboard */

/* === ANALYTICS HEADER SECTION === */
.analytics-header {
    background: linear-gradient(135deg,
        rgba(126, 217, 87, 0.1) 0%,
        rgba(76, 175, 80, 0.05) 100%);
    border-radius: var(--vendor-radius-2xl);
    padding: var(--vendor-space-8);
    margin-bottom: var(--vendor-space-8);
    border: 1px solid var(--vendor-border);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    background-color: rgba(255, 255, 255, 0.8);
}

.analytics-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-accent));
    border-radius: 50%;
    opacity: 0.1;
    transition: var(--vendor-transition);
}

.header-content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--vendor-space-6);
}

.header-text {
    flex: 1;
}

.analytics-title {
    font-size: var(--vendor-font-size-3xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2) 0;
    line-height: 1.3;
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--vendor-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.analytics-subtitle {
    font-size: var(--vendor-font-size-base);
    color: var(--vendor-text-muted);
    margin: 0 0 var(--vendor-space-6) 0;
    line-height: 1.5;
}

.date-range-selector {
    display: flex;
    gap: var(--vendor-space-1);
    background: rgba(255, 255, 255, 0.9);
    padding: var(--vendor-space-1);
    border-radius: var(--vendor-radius-xl);
    border: 1px solid var(--vendor-border);
    backdrop-filter: blur(10px);
}

.date-range-btn {
    background: none;
    border: none;
    color: var(--vendor-text-muted);
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--vendor-transition);
    white-space: nowrap;
}

.date-range-btn:hover {
    background: var(--vendor-gray-100);
    color: var(--vendor-text-primary);
}

.date-range-btn.active {
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-accent));
    color: white;
    box-shadow: var(--vendor-shadow-md);
}

/* === MODERN KPI GRID === */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

.kpi-card {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    padding: var(--vendor-space-6);
    border: 1px solid var(--vendor-border);
    transition: var(--vendor-transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
}

.kpi-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--vendor-shadow-xl);
    border-color: var(--vendor-primary);
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--kpi-color, var(--vendor-primary)), var(--vendor-accent));
}

.kpi-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100px;
    height: 100px;
    background: var(--kpi-color, var(--vendor-primary));
    border-radius: 50%;
    opacity: 0.05;
    transition: var(--vendor-transition);
}

.kpi-card:hover::after {
    transform: scale(1.5);
    opacity: 0.1;
}

.kpi-card.revenue { --kpi-color: var(--vendor-primary); }
.kpi-card.orders { --kpi-color: var(--vendor-accent); }
.kpi-card.customers { --kpi-color: var(--vendor-success); }
.kpi-card.conversion { --kpi-color: var(--vendor-warning); }

.kpi-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--vendor-space-5);
}

.kpi-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-xl);
    color: white;
    background: linear-gradient(135deg, var(--kpi-color), var(--vendor-accent));
    box-shadow: var(--vendor-shadow-md);
    position: relative;
    overflow: hidden;
}

.kpi-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.kpi-card:hover .kpi-icon::before {
    transform: translateX(100%);
}

.kpi-menu {
    background: none;
    border: none;
    color: var(--vendor-text-light);
    font-size: var(--vendor-font-size-sm);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: var(--vendor-radius);
    transition: var(--vendor-transition);
}

.kpi-menu:hover {
    background: var(--vendor-gray-100);
    color: var(--vendor-text-primary);
}

.kpi-content {
    position: relative;
    z-index: 1;
}

.kpi-label {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--vendor-space-2);
}

.kpi-value {
    font-size: var(--vendor-font-size-3xl);
    font-weight: 800;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-3);
    line-height: 1;
    font-family: var(--vendor-font-mono);
    background: linear-gradient(135deg, var(--vendor-text-primary), var(--kpi-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.kpi-change {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-full);
    backdrop-filter: blur(10px);
    margin-bottom: var(--vendor-space-4);
}

.kpi-change.positive {
    background: rgba(76, 175, 80, 0.1);
    color: var(--vendor-success);
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.kpi-change.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--vendor-danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.kpi-change.neutral {
    background: rgba(107, 114, 128, 0.1);
    color: var(--vendor-text-muted);
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.kpi-sparkline {
    height: 50px;
    margin-top: var(--vendor-space-3);
    border-radius: var(--vendor-radius);
    overflow: hidden;
}

/* === CHART GRID === */
.chart-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

.analytics-chart-card {
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-2xl);
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
}

.chart-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-title {
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: var(--vendor-space-1);
    background: rgba(255, 255, 255, 0.9);
    padding: var(--vendor-space-1);
    border-radius: var(--vendor-radius-lg);
    border: 1px solid var(--vendor-border);
}

.chart-control {
    background: none;
    border: none;
    color: var(--vendor-text-muted);
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius);
    font-size: var(--vendor-font-size-xs);
    font-weight: 500;
    cursor: pointer;
    transition: var(--vendor-transition);
    white-space: nowrap;
}

.chart-control:hover {
    background: var(--vendor-gray-100);
    color: var(--vendor-text-primary);
}

.chart-control.active {
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-accent));
    color: white;
    box-shadow: var(--vendor-shadow-sm);
}

.chart-body {
    padding: var(--vendor-space-6);
    min-height: 350px;
    position: relative;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Ensure canvas elements are properly sized */
.chart-body canvas {
    max-width: 100% !important;
    height: auto !important;
}

/* Chart loading state */
.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    color: var(--vendor-text-muted);
    font-size: var(--vendor-font-size-sm);
}

.chart-loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid var(--vendor-border);
    border-top: 2px solid var(--vendor-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1024px) {
    .kpi-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--vendor-space-4);
    }

    .chart-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

@media (max-width: 768px) {
    .analytics-header {
        padding: var(--vendor-space-6);
    }

    .header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--vendor-space-4);
    }

    .analytics-title {
        font-size: var(--vendor-font-size-2xl);
    }

    .date-range-selector {
        flex-wrap: wrap;
        justify-content: center;
    }

    .kpi-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }

    .kpi-card {
        padding: var(--vendor-space-5);
    }

    .kpi-icon {
        width: 48px;
        height: 48px;
    }

    .kpi-value {
        font-size: var(--vendor-font-size-2xl);
    }
}

/* === ANALYTICS TABLES SECTION === */
.analytics-table-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

.analytics-table-card {
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-2xl);
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
}

.table-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title {
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0;
}

.stat-link {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--vendor-transition);
}

.stat-link:hover {
    color: var(--vendor-primary-dark);
    text-decoration: underline;
}

.table-body {
    padding: var(--vendor-space-6);
    max-height: 400px;
    overflow-y: auto;
}

.analytics-table {
    width: 100%;
    border-collapse: collapse;
}

.analytics-table th {
    text-align: left;
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    color: var(--vendor-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border-bottom: 1px solid var(--vendor-border);
}

.analytics-table td {
    padding: var(--vendor-space-4);
    border-bottom: 1px solid var(--vendor-border-light);
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-primary);
}

.analytics-table tr:hover {
    background: var(--vendor-gray-50);
}

.analytics-table tr:last-child td {
    border-bottom: none;
}

/* === TOP PRODUCTS SECTION === */
.top-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

.top-products-card {
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-2xl);
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
}

.products-list {
    padding: var(--vendor-space-6);
    max-height: 400px;
    overflow-y: auto;
}

.product-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    padding: var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    transition: var(--vendor-transition);
    margin-bottom: var(--vendor-space-3);
}

.product-item:hover {
    background: var(--vendor-gray-50);
    transform: translateX(4px);
}

.product-item:last-child {
    margin-bottom: 0;
}

.product-rank {
    width: 32px;
    height: 32px;
    border-radius: var(--vendor-radius-full);
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-accent));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    flex-shrink: 0;
}

.product-info {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-1) 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-sales {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin: 0;
}

.product-revenue {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    white-space: nowrap;
    flex-shrink: 0;
}

/* === LOADING STATES === */
.loading-skeleton {
    background: linear-gradient(90deg, var(--vendor-gray-100) 25%, var(--vendor-gray-50) 50%, var(--vendor-gray-100) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--vendor-radius);
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.kpi-skeleton {
    height: 24px;
    margin-bottom: var(--vendor-space-3);
}

.chart-skeleton {
    height: 300px;
    border-radius: var(--vendor-radius-lg);
}

/* === ADDITIONAL RESPONSIVE STYLES === */
@media (max-width: 1024px) {
    .analytics-table-section {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }

    .top-products-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

@media (max-width: 640px) {
    .date-range-selector {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--vendor-space-1);
    }

    .date-range-btn {
        padding: var(--vendor-space-2);
        font-size: var(--vendor-font-size-xs);
    }

    .analytics-table th,
    .analytics-table td {
        padding: var(--vendor-space-2) var(--vendor-space-3);
        font-size: var(--vendor-font-size-xs);
    }

    .product-item {
        padding: var(--vendor-space-3);
        gap: var(--vendor-space-3);
    }

    .product-rank {
        width: 28px;
        height: 28px;
        font-size: var(--vendor-font-size-xs);
    }
}

/* === EXPORT ANALYTICS SECTION === */
.export-analytics-section {
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-2xl);
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--vendor-space-8);
}

.export-header {
    padding: var(--vendor-space-8);
    background: linear-gradient(135deg,
        rgba(126, 217, 87, 0.05) 0%,
        rgba(76, 175, 80, 0.02) 100%);
    border-bottom: 1px solid var(--vendor-border);
    position: relative;
    overflow: hidden;
}

.export-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-accent));
    border-radius: 50%;
    opacity: 0.08;
    transition: var(--vendor-transition);
}

.export-header-content {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    position: relative;
    z-index: 1;
}

.export-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--vendor-radius-xl);
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-accent));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-xl);
    color: white;
    box-shadow: var(--vendor-shadow-md);
    flex-shrink: 0;
}

.export-text {
    flex: 1;
}

.export-title {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2) 0;
    line-height: 1.3;
}

.export-description {
    font-size: var(--vendor-font-size-base);
    color: var(--vendor-text-muted);
    margin: 0;
    line-height: 1.5;
}

.export-cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--vendor-space-6);
    padding: var(--vendor-space-8);
}

.export-card {
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    transition: var(--vendor-transition);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--vendor-space-4);
}

.export-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--vendor-shadow-lg);
    border-color: var(--export-color, var(--vendor-primary));
}

.export-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--export-color, var(--vendor-primary)), var(--vendor-accent));
}

.export-card.csv { --export-color: #10B981; }
.export-card.pdf { --export-color: #EF4444; }
.export-card.excel { --export-color: #059669; }

.export-card-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--vendor-radius-xl);
    background: linear-gradient(135deg, var(--export-color), var(--vendor-accent));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-xl);
    color: white;
    box-shadow: var(--vendor-shadow-md);
    position: relative;
    overflow: hidden;
}

.export-card-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.export-card:hover .export-card-icon::before {
    transform: translateX(100%);
}

.export-card-content {
    flex: 1;
}

.export-card-title {
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2) 0;
    line-height: 1.3;
}

.export-card-desc {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    margin: 0 0 var(--vendor-space-3) 0;
    line-height: 1.4;
}

.export-card-size {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-light);
    font-weight: 500;
    background: var(--vendor-gray-100);
    padding: var(--vendor-space-1) var(--vendor-space-3);
    border-radius: var(--vendor-radius-full);
    display: inline-block;
}

.export-card-btn {
    background: linear-gradient(135deg, var(--export-color), var(--vendor-accent));
    color: white;
    border: none;
    padding: var(--vendor-space-3) var(--vendor-space-5);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--vendor-transition);
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    box-shadow: var(--vendor-shadow-sm);
    width: 100%;
    justify-content: center;
}

.export-card-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-md);
}

.export-card-btn:active {
    transform: translateY(0);
}

/* === EXPORT SECTION RESPONSIVE === */
@media (max-width: 1024px) {
    .export-cards-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

@media (max-width: 768px) {
    .export-header {
        padding: var(--vendor-space-6);
    }

    .export-header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--vendor-space-3);
    }

    .export-icon {
        width: 56px;
        height: 56px;
    }

    .export-title {
        font-size: var(--vendor-font-size-xl);
    }

    .export-cards-grid {
        padding: var(--vendor-space-6);
        gap: var(--vendor-space-4);
    }

    .export-card {
        padding: var(--vendor-space-5);
    }

    .export-card-icon {
        width: 48px;
        height: 48px;
    }
}
