<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .chart-container {
            width: 800px;
            height: 400px;
            margin: 20px 0;
            border: 1px solid #ddd;
            padding: 20px;
        }
        canvas {
            max-width: 100%;
            height: 350px !important;
        }
    </style>
</head>
<body>
    <h1>Chart.js Integration Test</h1>
    
    <div class="chart-container">
        <h3>Test Chart</h3>
        <canvas id="testChart"></canvas>
    </div>

    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <script>
        console.log('Chart.js available:', typeof Chart !== 'undefined');
        
        if (typeof Chart !== 'undefined') {
            const ctx = document.getElementById('testChart');
            
            const testChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Test Data',
                        data: [12, 19, 3, 5, 2, 3],
                        borderColor: '#7ED957',
                        backgroundColor: 'rgba(126, 217, 87, 0.1)',
                        fill: true,
                        tension: 0.4,
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            console.log('Test chart created successfully');
        } else {
            console.error('Chart.js not loaded');
            document.getElementById('testChart').innerHTML = '<p style="color: red;">Chart.js not loaded!</p>';
        }
    </script>
</body>
</html>
