@extends('layouts.vendor')

@section('title', 'Analytics')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-analytics.css') }}">
@endpush

@section('content')

<div x-data="vendorAnalyticsComponent()">
    <!-- Analytics Header -->
    <div class="analytics-header">
        <div class="header-content">
            <div class="header-text">
                <h1 class="analytics-title">Store Analytics</h1>
                <p class="analytics-subtitle">Track your performance and grow your business with data-driven insights</p>

                <div class="date-range-selector">
                    <button class="date-range-btn active" @click="setTimeRange('day')">Today</button>
                    <button class="date-range-btn" @click="setTimeRange('week')">7 Days</button>
                    <button class="date-range-btn" @click="setTimeRange('month')">30 Days</button>
                    <button class="date-range-btn" @click="setTimeRange('quarter')">90 Days</button>
                    <button class="date-range-btn" @click="setTimeRange('year')">1 Year</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <template x-if="loading">
        <div class="d-flex justify-center items-center" style="min-height: 400px;">
            <div class="loading-spinner"></div>
        </div>
    </template>

    <!-- Stats Cards -->
    <div class="stats-grid" x-show="!loading">
        <!-- Revenue Stats -->
        <div class="stats-card sales">
            <div class="stats-header">
                <div class="stats-icon">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <div class="stats-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.revenueChange || '+15.2%'">+15.2%</span>
                </div>
            </div>
            <div class="stats-label">Total Revenue</div>
            <div class="stats-value" x-text="formatCurrency(analyticsData?.revenue || 0)">₹2,45,680</div>
        </div>

        <!-- Orders Stats -->
        <div class="stats-card orders">
            <div class="stats-header">
                <div class="stats-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stats-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.ordersChange || '+8.5%'">+8.5%</span>
                </div>
            </div>
            <div class="stats-label">Total Orders</div>
            <div class="stats-value" x-text="analyticsData?.orders || 0">1,234</div>
        </div>

        <!-- Customers Stats -->
        <div class="stats-card customers">
            <div class="stats-header">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.customersChange || '+12.3%'">+12.3%</span>
                </div>
            </div>
            <div class="stats-label">New Customers</div>
            <div class="stats-value" x-text="analyticsData?.customers || 0">89</div>
        </div>

        <!-- Conversion Rate Stats -->
        <div class="stats-card products">
            <div class="stats-header">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-trend down">
                    <i class="fas fa-arrow-down"></i>
                    <span x-text="analyticsData?.conversionChange || '-0.5%'">-0.5%</span>
                </div>
            </div>
            <div class="stats-label">Conversion Rate</div>
            <div class="stats-value" x-text="(analyticsData?.conversionRate || 0) + '%'">3.2%</div>
        </div>
    </div>

    <!-- Chart Grid -->
    <div class="chart-grid">
        <!-- Revenue Chart -->
        <div class="analytics-chart-card">
            <div class="chart-header">
                <h3 class="chart-title" x-text="getChartTitle()">Revenue Trends</h3>
                <div class="chart-controls">
                    <button class="chart-control" :class="{ 'active': chartMetric === 'revenue' }" @click="setChartMetric('revenue')">Revenue</button>
                    <button class="chart-control" :class="{ 'active': chartMetric === 'orders' }" @click="setChartMetric('orders')">Orders</button>
                    <button class="chart-control" :class="{ 'active': chartMetric === 'customers' }" @click="setChartMetric('customers')">Customers</button>
                </div>
            </div>
            <div class="chart-body">
                <div x-show="loading" class="chart-loading">
                    Loading chart data...
                </div>
                <div class="chart-container" x-show="!loading">
                    <canvas id="revenueChart"></canvas>
                </div>

            </div>
        </div>

        <!-- Top Products -->
        <div class="analytics-chart-card">
            <div class="chart-header">
                <h3 class="chart-title">Top Performing Products</h3>
                <a href="{{ route('vendor.products.index') }}" class="stat-link">View all products</a>
            </div>
            <div class="products-list">
                <div class="product-item">
                    <div class="product-rank">1</div>
                    <div class="product-info">
                        <div class="product-name">Premium Wireless Headphones</div>
                        <div class="product-sales">245 sales this month</div>
                    </div>
                    <div class="product-revenue">₹48,900</div>
                </div>

                <div class="product-item">
                    <div class="product-rank">2</div>
                    <div class="product-info">
                        <div class="product-name">Gaming Laptop Pro</div>
                        <div class="product-sales">89 sales this month</div>
                    </div>
                    <div class="product-revenue">₹89,100</div>
                </div>

                <div class="product-item">
                    <div class="product-rank">3</div>
                    <div class="product-info">
                        <div class="product-name">Smartphone Case</div>
                        <div class="product-sales">156 sales this month</div>
                    </div>
                    <div class="product-revenue">₹15,600</div>
                </div>

                <div class="product-item">
                    <div class="product-rank">4</div>
                    <div class="product-info">
                        <div class="product-name">Smart Watch</div>
                        <div class="product-sales">67 sales this month</div>
                    </div>
                    <div class="product-revenue">₹20,100</div>
                </div>

                <div class="product-item">
                    <div class="product-rank">5</div>
                    <div class="product-info">
                        <div class="product-name">Digital Camera</div>
                        <div class="product-sales">34 sales this month</div>
                    </div>
                    <div class="product-revenue">₹34,000</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Tables Section -->
    <div class="analytics-table-section">
        <!-- Traffic Sources -->
        <div class="analytics-table-card">
            <div class="table-header">
                <h3 class="table-title">Traffic Sources</h3>
                <a href="#" class="stat-link">View details</a>
            </div>
            <div class="table-body">
                <table class="analytics-table">
                    <thead>
                        <tr>
                            <th>Source</th>
                            <th>Visitors</th>
                            <th>Conversion</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Direct</td>
                            <td>2,456</td>
                            <td>4.2%</td>
                        </tr>
                        <tr>
                            <td>Google Search</td>
                            <td>1,890</td>
                            <td>3.8%</td>
                        </tr>
                        <tr>
                            <td>Social Media</td>
                            <td>1,234</td>
                            <td>2.9%</td>
                        </tr>
                        <tr>
                            <td>Email</td>
                            <td>890</td>
                            <td>5.1%</td>
                        </tr>
                        <tr>
                            <td>Referral</td>
                            <td>567</td>
                            <td>3.2%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Customer Demographics -->
        <div class="analytics-table-card">
            <div class="table-header">
                <h3 class="table-title">Customer Demographics</h3>
                <a href="#" class="stat-link">View details</a>
            </div>
            <div class="table-body">
                <table class="analytics-table">
                    <thead>
                        <tr>
                            <th>Age Group</th>
                            <th>Customers</th>
                            <th>Revenue</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>18-25</td>
                            <td>1,234</td>
                            <td>₹45,600</td>
                        </tr>
                        <tr>
                            <td>26-35</td>
                            <td>2,890</td>
                            <td>₹89,200</td>
                        </tr>
                        <tr>
                            <td>36-45</td>
                            <td>1,567</td>
                            <td>₹67,800</td>
                        </tr>
                        <tr>
                            <td>46-55</td>
                            <td>890</td>
                            <td>₹34,500</td>
                        </tr>
                        <tr>
                            <td>55+</td>
                            <td>456</td>
                            <td>₹18,900</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Export Analytics Section -->
    <div class="export-analytics-section">
        <div class="export-header">
            <div class="export-content">
                <h3 class="export-title">Export Analytics Data</h3>
                <p class="export-subtitle">Download your analytics data in various formats for further analysis</p>
            </div>
            <div class="export-actions">
                <button class="export-btn">
                    <i class="fas fa-file-csv"></i>
                    <span>CSV</span>
                </button>
                <button class="export-btn">
                    <i class="fas fa-file-excel"></i>
                    <span>Excel</span>
                </button>
                <button class="export-btn">
                    <i class="fas fa-file-pdf"></i>
                    <span>PDF</span>
                </button>
                <button class="export-btn primary">
                    <i class="fas fa-download"></i>
                    <span>Download</span>
                </button>
            </div>
        </div>
    </div>
</div>

@section('page-title', 'Analytics')
@section('page-subtitle', 'Track your performance and grow your business')

@endsection
