@extends('layouts.vendor')

@section('title', 'Analytics')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-analytics.css') }}">
@endpush

@section('content')
<div x-data="vendorAnalytics()">
    <!-- Analytics Header -->
    <div class="analytics-header">
        <div class="header-content">
            <div class="header-text">
                <h1 class="analytics-title">Store Analytics</h1>
                <p class="analytics-subtitle">Track your performance and grow your business with data-driven insights</p>

                <div class="date-range-selector">
                    <button class="date-range-btn active" @click="setTimeRange('day')">Today</button>
                    <button class="date-range-btn" @click="setTimeRange('week')">7 Days</button>
                    <button class="date-range-btn" @click="setTimeRange('month')">30 Days</button>
                    <button class="date-range-btn" @click="setTimeRange('quarter')">90 Days</button>
                    <button class="date-range-btn" @click="setTimeRange('year')">1 Year</button>
                </div>
            </div>


        </div>
    </div>

    <!-- Loading State -->
    <template x-if="loading">
        <div class="d-flex justify-center items-center" style="min-height: 400px;">
            <div class="loading-spinner"></div>
        </div>
    </template>

    <!-- KPI Grid -->
    <div class="kpi-grid" x-show="!loading">
        <!-- Revenue KPI -->
        <div class="kpi-card revenue">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <button class="kpi-menu">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
            <div class="kpi-content">
                <div class="kpi-label">Total Revenue</div>
                <div class="kpi-value" x-text="formatCurrency(analyticsData?.revenue || 0)">₹2,45,680</div>
                <div class="kpi-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.revenueChange || '+15.2%'">+15.2% from last period</span>
                </div>
            </div>
            <div class="kpi-sparkline">
                <canvas id="revenueSparkline" width="200" height="40"></canvas>
            </div>
        </div>

        <!-- Orders KPI -->
        <div class="kpi-card orders">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <button class="kpi-menu">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
            <div class="kpi-content">
                <div class="kpi-label">Total Orders</div>
                <div class="kpi-value" x-text="analyticsData?.orders || 0">1,234</div>
                <div class="kpi-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.ordersChange || '+8.5%'">+8.5% from last period</span>
                </div>
            </div>
            <div class="kpi-sparkline">
                <canvas id="ordersSparkline" width="200" height="40"></canvas>
            </div>
        </div>

        <!-- Customers KPI -->
        <div class="kpi-card customers">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-users"></i>
                </div>
                <button class="kpi-menu">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
            <div class="kpi-content">
                <div class="kpi-label">New Customers</div>
                <div class="kpi-value" x-text="analyticsData?.customers || 0">89</div>
                <div class="kpi-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.customersChange || '+12.3%'">+12.3% from last period</span>
                </div>
            </div>
            <div class="kpi-sparkline">
                <canvas id="customersSparkline" width="200" height="40"></canvas>
            </div>
        </div>

        <!-- Conversion Rate KPI -->
        <div class="kpi-card conversion">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <button class="kpi-menu">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
            <div class="kpi-content">
                <div class="kpi-label">Conversion Rate</div>
                <div class="kpi-value" x-text="(analyticsData?.conversionRate || 0) + '%'">3.2%</div>
                <div class="kpi-change negative">
                    <i class="fas fa-arrow-down"></i>
                    <span x-text="analyticsData?.conversionChange || '-0.5%'">-0.5% from last period</span>
                </div>
            </div>
            <div class="kpi-sparkline">
                <canvas id="conversionSparkline" width="200" height="40"></canvas>
            </div>
        </div>
    </div>

    <!-- Chart Grid -->
    <div class="chart-grid">
        <!-- Revenue Chart -->
        <div class="analytics-chart-card">
            <div class="chart-header">
                <h3 class="chart-title" x-text="getChartTitle()">Revenue Trends</h3>
                <div class="chart-controls">
                    <button class="chart-control" :class="{ 'active': chartMetric === 'revenue' }" @click="setChartMetric('revenue')">Revenue</button>
                    <button class="chart-control" :class="{ 'active': chartMetric === 'orders' }" @click="setChartMetric('orders')">Orders</button>
                    <button class="chart-control" :class="{ 'active': chartMetric === 'customers' }" @click="setChartMetric('customers')">Customers</button>
                </div>
            </div>
            <div class="chart-body">
                <div x-show="loading" class="chart-loading">
                    Loading chart data...
                </div>
                <div class="chart-container" x-show="!loading">
                    <canvas id="revenueChart"></canvas>
                </div>
                <!-- Debug info (remove in production) -->
                <div x-show="!loading" style="font-size: 12px; color: #666; margin-top: 10px;">
                    <div>Time Range: <span x-text="timeRange"></span></div>
                    <div>Chart Metric: <span x-text="chartMetric"></span></div>
                    <div>Data Points: <span x-text="chartData.labels ? chartData.labels.length : 0"></span></div>
                    <button @click="renderRevenueChart()" style="margin-top: 5px; padding: 5px 10px; background: #66BB6A; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        Reload Chart
                    </button>
                </div>
            </div>
        </div>

        <!-- Top Products -->
        <div class="analytics-chart-card">
            <div class="chart-header">
                <h3 class="chart-title">Top Performing Products</h3>
                <a href="{{ route('vendor.products.index') }}" class="stat-link">View all products</a>
            </div>
            <div class="products-list">
                <div class="product-item">
                    <div class="product-rank">1</div>
                    <div class="product-info">
                        <div class="product-name">Premium Wireless Headphones</div>
                        <div class="product-sales">245 sales this month</div>
                    </div>
                    <div class="product-revenue">₹48,900</div>
                </div>

                <div class="product-item">
                    <div class="product-rank">2</div>
                    <div class="product-info">
                        <div class="product-name">Gaming Laptop Pro</div>
                        <div class="product-sales">89 sales this month</div>
                    </div>
                    <div class="product-revenue">₹89,100</div>
                </div>

                <div class="product-item">
                    <div class="product-rank">3</div>
                    <div class="product-info">
                        <div class="product-name">Smartphone Case</div>
                        <div class="product-sales">156 sales this month</div>
                    </div>
                    <div class="product-revenue">₹15,600</div>
                </div>

                <div class="product-item">
                    <div class="product-rank">4</div>
                    <div class="product-info">
                        <div class="product-name">Smart Watch</div>
                        <div class="product-sales">67 sales this month</div>
                    </div>
                    <div class="product-revenue">₹20,100</div>
                </div>

                <div class="product-item">
                    <div class="product-rank">5</div>
                    <div class="product-info">
                        <div class="product-name">Digital Camera</div>
                        <div class="product-sales">34 sales this month</div>
                    </div>
                    <div class="product-revenue">₹34,000</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Tables Section -->
    <div class="analytics-table-section">
        <!-- Traffic Sources -->
        <div class="analytics-table-card">
            <div class="table-header">
                <h3 class="table-title">Traffic Sources</h3>
                <a href="#" class="stat-link">View details</a>
            </div>
            <div class="table-body">
                <table class="analytics-table">
                    <thead>
                        <tr>
                            <th>Source</th>
                            <th>Visitors</th>
                            <th>Conversion</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Direct</td>
                            <td>2,456</td>
                            <td>4.2%</td>
                        </tr>
                        <tr>
                            <td>Google Search</td>
                            <td>1,890</td>
                            <td>3.8%</td>
                        </tr>
                        <tr>
                            <td>Social Media</td>
                            <td>1,234</td>
                            <td>2.9%</td>
                        </tr>
                        <tr>
                            <td>Email</td>
                            <td>890</td>
                            <td>5.1%</td>
                        </tr>
                        <tr>
                            <td>Referral</td>
                            <td>567</td>
                            <td>3.2%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Customer Demographics -->
        <div class="analytics-table-card">
            <div class="table-header">
                <h3 class="table-title">Customer Demographics</h3>
                <a href="#" class="stat-link">View details</a>
            </div>
            <div class="table-body">
                <table class="analytics-table">
                    <thead>
                        <tr>
                            <th>Age Group</th>
                            <th>Customers</th>
                            <th>Revenue</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>18-24</td>
                            <td>234</td>
                            <td>₹45,600</td>
                        </tr>
                        <tr>
                            <td>25-34</td>
                            <td>456</td>
                            <td>₹89,200</td>
                        </tr>
                        <tr>
                            <td>35-44</td>
                            <td>345</td>
                            <td>₹78,900</td>
                        </tr>
                        <tr>
                            <td>45-54</td>
                            <td>234</td>
                            <td>₹56,700</td>
                        </tr>
                        <tr>
                            <td>55+</td>
                            <td>123</td>
                            <td>₹34,500</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Export Analytics Data Section -->
    <div class="export-analytics-section">
        <div class="export-header">
            <div class="export-header-content">
                <div class="export-icon">
                    <i class="fas fa-download"></i>
                </div>
                <div class="export-text">
                    <h3 class="export-title">Export Analytics Data</h3>
                    <p class="export-description">Download your analytics data in various formats for further analysis and reporting</p>
                </div>
            </div>
        </div>

        <div class="export-cards-grid">
            <div class="export-card csv">
                <div class="export-card-icon">
                    <i class="fas fa-file-csv"></i>
                </div>
                <div class="export-card-content">
                    <h4 class="export-card-title">CSV Format</h4>
                    <p class="export-card-desc">Raw data for spreadsheet analysis</p>
                    <div class="export-card-size">~2.5 MB</div>
                </div>
                <button class="export-card-btn">
                    <i class="fas fa-download"></i>
                    <span>Download</span>
                </button>
            </div>

            <div class="export-card pdf">
                <div class="export-card-icon">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <div class="export-card-content">
                    <h4 class="export-card-title">PDF Report</h4>
                    <p class="export-card-desc">Formatted report with charts</p>
                    <div class="export-card-size">~1.8 MB</div>
                </div>
                <button class="export-card-btn">
                    <i class="fas fa-download"></i>
                    <span>Download</span>
                </button>
            </div>

            <div class="export-card excel">
                <div class="export-card-icon">
                    <i class="fas fa-file-excel"></i>
                </div>
                <div class="export-card-content">
                    <h4 class="export-card-title">Excel Workbook</h4>
                    <p class="export-card-desc">Advanced analysis with formulas</p>
                    <div class="export-card-size">~3.2 MB</div>
                </div>
                <button class="export-card-btn">
                    <i class="fas fa-download"></i>
                    <span>Download</span>
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, Chart.js available:', typeof Chart !== 'undefined');
});

function vendorAnalytics() {
    return {
        loading: true,
        timeRange: 'day',
        chartMetric: 'revenue',
        analyticsData: {
            revenue: 245680,
            revenueChange: '+15.2%',
            orders: 1234,
            ordersChange: '+8.5%',
            customers: 89,
            customersChange: '+12.3%',
            conversionRate: 3.2,
            conversionChange: '-0.5%'
        },
        revenueChart: null,
        chartData: {},

        init() {
            console.log('Initializing vendor analytics...');
            console.log('Chart.js available:', typeof Chart !== 'undefined');

            // Generate initial data
            this.generateChartData();

            // Set loading to false first
            this.loading = false;

            // Wait for Alpine.js to update the DOM
            this.$nextTick(() => {
                // Additional delay to ensure canvas is rendered
                setTimeout(() => {
                    console.log('Attempting to render charts...');
                    this.renderRevenueChart();
                    this.renderSparklines();
                }, 200);
            });
        },

        setTimeRange(range) {
            this.timeRange = range;
            // Update active button
            document.querySelectorAll('.date-range-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            this.fetchAnalyticsData();
        },

        setChartMetric(metric) {
            this.chartMetric = metric;
            setTimeout(() => {
                this.renderRevenueChart();
            }, 50);
        },

        getChartTitle() {
            const titles = {
                revenue: 'Revenue Trends',
                orders: 'Orders Trends',
                customers: 'Customer Trends'
            };
            return titles[this.chartMetric] || 'Revenue Trends';
        },

        getTimeRangeLabel() {
            const labels = {
                day: 'Today (24 Hours)',
                week: '7 Days',
                month: '30 Days',
                quarter: '90 Days (Quarterly)',
                year: '1 Year'
            };
            return labels[this.timeRange] || '30 Days';
        },

        fetchAnalyticsData() {
            this.loading = true;
            // Simulate API call
            setTimeout(() => {
                this.generateChartData();
                this.loading = false;
                this.$nextTick(() => {
                    this.renderRevenueChart();
                    this.renderSparklines();
                });
            }, 500);
        },

        generateChartData() {
            console.log('Generating chart data for timeRange:', this.timeRange);
            const now = new Date();

            switch(this.timeRange) {
                case 'day':
                    this.chartData = this.generateHourlyData();
                    break;
                case 'week':
                    this.chartData = this.generateWeeklyData();
                    break;
                case 'month':
                    this.chartData = this.generateMonthlyData();
                    break;
                case 'quarter':
                    this.chartData = this.generateQuarterlyData();
                    break;
                case 'year':
                    this.chartData = this.generateYearlyData();
                    break;
                default:
                    this.chartData = this.generateMonthlyData();
            }

            console.log('Generated chart data:', this.chartData);
        },

        generateHourlyData() {
            const labels = [];
            const revenueData = [];
            const ordersData = [];
            const customersData = [];

            // Generate 24 hours of data
            for (let i = 0; i < 24; i++) {
                labels.push(`${i.toString().padStart(2, '0')}:00`);

                // Simulate realistic hourly patterns
                let baseRevenue = 5000;
                if (i >= 9 && i <= 17) baseRevenue = 15000; // Business hours
                if (i >= 19 && i <= 22) baseRevenue = 12000; // Evening peak

                revenueData.push(baseRevenue + Math.random() * 8000);
                ordersData.push(Math.floor((baseRevenue / 500) + Math.random() * 20));
                customersData.push(Math.floor((baseRevenue / 1000) + Math.random() * 10));
            }

            return {
                labels,
                datasets: {
                    revenue: revenueData,
                    orders: ordersData,
                    customers: customersData
                }
            };
        },

        generateWeeklyData() {
            const labels = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            const revenueData = [];
            const ordersData = [];
            const customersData = [];

            // Generate weekly data with weekend/weekday patterns
            labels.forEach((day, index) => {
                let baseRevenue = 45000;
                if (index === 0 || index === 6) baseRevenue = 35000; // Weekends
                if (index === 5) baseRevenue = 55000; // Friday peak

                revenueData.push(baseRevenue + Math.random() * 20000);
                ordersData.push(Math.floor((baseRevenue / 300) + Math.random() * 50));
                customersData.push(Math.floor((baseRevenue / 800) + Math.random() * 30));
            });

            return {
                labels,
                datasets: {
                    revenue: revenueData,
                    orders: ordersData,
                    customers: customersData
                }
            };
        },

        generateMonthlyData() {
            const labels = [];
            const revenueData = [];
            const ordersData = [];
            const customersData = [];

            // Generate 30 days of data
            for (let i = 1; i <= 30; i++) {
                labels.push(`${i}`);

                let baseRevenue = 35000;
                // Add some monthly trends
                if (i <= 5 || i >= 25) baseRevenue = 45000; // Month start/end peaks
                if (i >= 15 && i <= 20) baseRevenue = 30000; // Mid-month dip

                revenueData.push(baseRevenue + Math.random() * 25000);
                ordersData.push(Math.floor((baseRevenue / 250) + Math.random() * 60));
                customersData.push(Math.floor((baseRevenue / 700) + Math.random() * 40));
            }

            return {
                labels,
                datasets: {
                    revenue: revenueData,
                    orders: ordersData,
                    customers: customersData
                }
            };
        },

        generateQuarterlyData() {
            const labels = [];
            const revenueData = [];
            const ordersData = [];
            const customersData = [];

            // Generate 13 weeks of data (90 days / 7)
            for (let i = 1; i <= 13; i++) {
                labels.push(`Week ${i}`);

                let baseRevenue = 280000;
                // Add quarterly trends
                if (i <= 3) baseRevenue = 320000; // Quarter start
                if (i >= 11) baseRevenue = 350000; // Quarter end push

                revenueData.push(baseRevenue + Math.random() * 100000);
                ordersData.push(Math.floor((baseRevenue / 200) + Math.random() * 200));
                customersData.push(Math.floor((baseRevenue / 600) + Math.random() * 150));
            }

            return {
                labels,
                datasets: {
                    revenue: revenueData,
                    orders: ordersData,
                    customers: customersData
                }
            };
        },

        generateYearlyData() {
            const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const revenueData = [];
            const ordersData = [];
            const customersData = [];

            // Generate yearly data with seasonal patterns
            labels.forEach((month, index) => {
                let baseRevenue = 1200000;
                // Seasonal adjustments
                if (index >= 10) baseRevenue = 1500000; // Holiday season
                if (index >= 3 && index <= 5) baseRevenue = 1350000; // Spring peak
                if (index >= 6 && index <= 8) baseRevenue = 1100000; // Summer dip

                revenueData.push(baseRevenue + Math.random() * 400000);
                ordersData.push(Math.floor((baseRevenue / 800) + Math.random() * 500));
                customersData.push(Math.floor((baseRevenue / 2000) + Math.random() * 300));
            });

            return {
                labels,
                datasets: {
                    revenue: revenueData,
                    orders: ordersData,
                    customers: customersData
                }
            };
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                minimumFractionDigits: 0
            }).format(amount);
        },

        renderRevenueChart() {
            console.log('=== CHART RENDERING DEBUG ===');
            console.log('Attempting to render chart...');

            const ctx = document.getElementById('revenueChart');
            console.log('Canvas element:', ctx);

            if (!ctx) {
                console.error('Chart canvas not found!');
                alert('Chart canvas not found! Check if the canvas element exists.');
                return;
            }

            // Check if Chart.js is loaded
            if (typeof Chart === 'undefined') {
                console.error('Chart.js is not loaded!');
                alert('Chart.js library is not loaded!');
                return;
            }

            console.log('Chart.js version:', Chart.version);

            if (this.revenueChart) {
                console.log('Destroying existing chart...');
                this.revenueChart.destroy();
                this.revenueChart = null;
            }

            console.log('Chart data:', this.chartData);
            console.log('Chart metric:', this.chartMetric);

            // Get chart colors based on vendor theme
            const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--vendor-primary').trim() || '#7ED957';
            const primaryRgb = this.hexToRgb(primaryColor);

            // Get data and formatting based on selected metric
            const metricConfig = this.getMetricConfig();
            const chartData = this.chartData.datasets?.[this.chartMetric] || [];

            // If no data, show empty chart with message
            if (!chartData.length) {
                console.warn('No chart data available for metric:', this.chartMetric);
                return;
            }

            try {
                console.log('Creating new Chart instance...');
                console.log('Canvas context:', ctx.getContext('2d'));

                this.revenueChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: this.chartData.labels || [],
                        datasets: [{
                            label: metricConfig.label,
                            data: chartData,
                            borderColor: metricConfig.color,
                            backgroundColor: `rgba(${this.hexToRgb(metricConfig.color).r}, ${this.hexToRgb(metricConfig.color).g}, ${this.hexToRgb(metricConfig.color).b}, 0.1)`,
                            fill: true,
                            tension: 0.4,
                            borderWidth: 3,
                            pointBackgroundColor: metricConfig.color,
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 4,
                            pointHoverRadius: 6
                        }]
                    },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: metricConfig.color,
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: false,
                            callbacks: {
                                label: (context) => {
                                    return `${metricConfig.label}: ${metricConfig.formatter(context.parsed.y)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)',
                                drawBorder: false
                            },
                            ticks: {
                                color: '#6b7280',
                                font: {
                                    size: 12
                                },
                                callback: function(value) {
                                    return metricConfig.yAxisFormatter(value);
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false,
                                drawBorder: false
                            },
                            ticks: {
                                color: '#6b7280',
                                font: {
                                    size: 12
                                },
                                maxTicksLimit: this.getMaxTicks()
                            }
                        }
                    },
                    elements: {
                        point: {
                            hoverBackgroundColor: metricConfig.color
                        }
                    }
                }
            });

            console.log('Chart created successfully');
            } catch (error) {
                console.error('Error creating chart:', error);
            }
        },

        getMetricConfig() {
            const configs = {
                revenue: {
                    label: 'Revenue',
                    color: '#7ED957',
                    formatter: (value) => this.formatCurrency(value),
                    yAxisFormatter: (value) => '₹' + (value / 1000).toFixed(0) + 'K'
                },
                orders: {
                    label: 'Orders',
                    color: '#3B82F6',
                    formatter: (value) => value.toLocaleString() + ' orders',
                    yAxisFormatter: (value) => value.toLocaleString()
                },
                customers: {
                    label: 'Customers',
                    color: '#F59E0B',
                    formatter: (value) => value.toLocaleString() + ' customers',
                    yAxisFormatter: (value) => value.toLocaleString()
                }
            };
            return configs[this.chartMetric] || configs.revenue;
        },

        getMaxTicks() {
            switch(this.timeRange) {
                case 'day': return 12; // Show every 2 hours
                case 'week': return 7;  // Show all days
                case 'month': return 15; // Show every 2 days
                case 'quarter': return 13; // Show all weeks
                case 'year': return 12; // Show all months
                default: return 10;
            }
        },

        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : { r: 126, g: 217, b: 87 }; // Default to vendor primary
        },

        renderSparklines() {
            // Render sparkline charts for KPIs with dynamic data
            const sparklineData = this.generateSparklineData();
            this.renderSparkline('revenueSparkline', sparklineData.revenue);
            this.renderSparkline('ordersSparkline', sparklineData.orders);
            this.renderSparkline('customersSparkline', sparklineData.customers);
            this.renderSparkline('conversionSparkline', sparklineData.conversion);
        },

        generateSparklineData() {
            // Generate 7 data points for sparklines (last 7 periods)
            const revenue = [];
            const orders = [];
            const customers = [];
            const conversion = [];

            for (let i = 0; i < 7; i++) {
                revenue.push(20000 + Math.random() * 25000);
                orders.push(50 + Math.random() * 30);
                customers.push(15 + Math.random() * 20);
                conversion.push(2.5 + Math.random() * 1.5);
            }

            return { revenue, orders, customers, conversion };
        },

        renderSparkline(canvasId, data) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) {
                console.warn(`Sparkline canvas ${canvasId} not found`);
                return;
            }

            if (typeof Chart === 'undefined') {
                console.error('Chart.js not available for sparkline');
                return;
            }

            const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--vendor-primary').trim() || '#7ED957';
            const primaryRgb = this.hexToRgb(primaryColor);

            try {
                new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array(data.length).fill(''),
                    datasets: [{
                        data: data,
                        borderColor: primaryColor,
                        backgroundColor: `rgba(${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}, 0.2)`,
                        fill: true,
                        tension: 0.4,
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: { enabled: false }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: {
                        point: { radius: 0 }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
            } catch (error) {
                console.error(`Error creating sparkline ${canvasId}:`, error);
            }
        }
    }
}
</script>
@endpush

@endsection

