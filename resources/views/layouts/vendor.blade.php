<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#7ED957">

    <title>@yield('title', 'Vendor Dashboard') - {{ config('app.name', 'WhaMart') }}</title>
    <meta name="description" content="@yield('description', 'Modern vendor dashboard for WhaMart - Manage your store, products, orders, and analytics')">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" as="style">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">

    <!-- Modern Vendor Layout CSS -->
    <link rel="stylesheet" href="{{ asset('css/vendor/vendor-layout.css') }}">

    <!-- Page-specific CSS -->
    @stack('page-styles')

    <!-- Chart Libraries (load before Alpine.js) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <!-- Alpine.js -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>



    <!-- Additional Scripts -->
    @stack('head-scripts')
</head>
<body class="vendor-body"
      x-data="vendorApp()"
      x-init="initApp()"
      :class="{
          'mobile-sidebar-open': mobileSidebarOpen,
          'sidebar-minimized': sidebarMinimized
      }"
      @resize.window="handleResize()"
      @keydown.escape="closeMobileSidebar()"
      @click.away="closeDropdowns()"

    <!-- Mobile Sidebar Overlay -->
    <div x-show="mobileSidebarOpen"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         @click="closeMobileSidebar()"
         class="vendor-mobile-overlay"
         :class="{ 'active': mobileSidebarOpen }"></div>

    <!-- Main App Container -->
    <div class="vendor-app">

        <!-- Vendor Sidebar -->
        <aside class="vendor-sidebar"
               :class="{ 'mobile-open': mobileSidebarOpen }">

            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="{{ route('vendor.dashboard') }}" class="sidebar-brand">
                    <div class="brand-logo">
                        <i class="fas fa-store"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">WhaMart</div>
                        <div class="brand-subtitle">Vendor Panel</div>
                    </div>
                </a>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="{{ route('vendor.dashboard') }}"
                               class="nav-link {{ request()->routeIs('vendor.dashboard') ? 'active' : '' }}"
                               data-tooltip="Dashboard">
                                <i class="nav-icon fas fa-home"></i>
                                <span class="nav-item-text">Dashboard</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.analytics') }}"
                               class="nav-link {{ request()->routeIs('vendor.analytics') ? 'active' : '' }}"
                               data-tooltip="Analytics">
                                <i class="nav-icon fas fa-chart-line"></i>
                                <span class="nav-item-text">Analytics</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.orders') }}"
                               class="nav-link {{ request()->routeIs('vendor.orders*') ? 'active' : '' }}"
                               data-tooltip="Orders">
                                <i class="nav-icon fas fa-shopping-bag"></i>
                                <span class="nav-item-text">Orders</span>
                                <span class="nav-badge">12</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.products.index') }}"
                               class="nav-link {{ request()->routeIs('vendor.products*') ? 'active' : '' }}"
                               data-tooltip="Products">
                                <i class="nav-icon fas fa-box"></i>
                                <span class="nav-item-text">Products</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.customers') }}"
                               class="nav-link {{ request()->routeIs('vendor.customers*') ? 'active' : '' }}"
                               data-tooltip="Customers">
                                <i class="nav-icon fas fa-users"></i>
                                <span class="nav-item-text">Customers</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Settings</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="{{ route('vendor.settings') }}"
                               class="nav-link {{ request()->routeIs('vendor.settings*') ? 'active' : '' }}"
                               data-tooltip="Settings">
                                <i class="nav-icon fas fa-cog"></i>
                                <span class="nav-item-text">Settings</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.support') }}"
                               class="nav-link {{ request()->routeIs('vendor.support*') ? 'active' : '' }}"
                               data-tooltip="Support">
                                <i class="nav-icon fas fa-life-ring"></i>
                                <span class="nav-item-text">Support</span>
                            </a>
                        </li>
                    </ul>
                </div>

            </nav>
        </aside>

        <!-- Main Content Wrapper -->
        <div class="vendor-main-wrapper">
            <!-- Header -->
            <header class="vendor-header">
                <div class="header-content">
                    <div class="header-left">
                        <!-- Sidebar Toggle -->
                        <button @click="toggleSidebar()" class="sidebar-toggle">
                            <i class="fas fa-bars"></i>
                        </button>

                        <!-- Mobile Menu Toggle -->
                        <button @click="toggleMobileSidebar()" class="mobile-menu-toggle">
                            <i class="fas fa-bars"></i>
                        </button>

                        <!-- Page Title -->
                        <div class="header-title">
                            <h1 class="page-title">@yield('page-title', 'Dashboard')</h1>
                            <p class="page-subtitle">@yield('page-subtitle', 'Manage your store and track performance')</p>
                        </div>
                    </div>

                    <div class="header-right">
                        <!-- Notifications -->
                        <div class="dropdown" x-data="{ open: false }">
                            <button @click="open = !open" class="header-action-btn">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                            <div x-show="open" @click.away="open = false" class="dropdown-menu">
                                <div class="dropdown-header">
                                    <h3>Notifications</h3>
                                </div>
                                <a href="#" class="dropdown-item">
                                    <i class="fas fa-shopping-bag text-primary"></i>
                                    <span>New order received</span>
                                </a>
                                <a href="#" class="dropdown-item">
                                    <i class="fas fa-star text-primary"></i>
                                    <span>New review posted</span>
                                </a>
                            </div>
                        </div>

                        <!-- User Profile -->
                        <div class="dropdown" x-data="{ open: false }">
                            <button @click="open = !open" class="user-profile">
                                <div class="user-avatar">
                                    {{ substr(auth()->user()->name ?? 'V', 0, 1) }}
                                </div>
                                <div class="user-info">
                                    <div class="user-name">{{ auth()->user()->name ?? 'Vendor' }}</div>
                                    <div class="user-role">Vendor</div>
                                </div>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div x-show="open" @click.away="open = false" class="dropdown-menu">
                                <a href="{{ route('vendor.profile') }}" class="dropdown-item">
                                    <i class="fas fa-user"></i>
                                    <span>Profile</span>
                                </a>
                                <a href="{{ route('vendor.settings') }}" class="dropdown-item">
                                    <i class="fas fa-cog"></i>
                                    <span>Settings</span>
                                </a>
                                <div class="dropdown-divider"></div>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt"></i>
                                        <span>Logout</span>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="vendor-main">
                <div class="page-content">
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Page Scripts -->
    @stack('page-scripts')

    <!-- Vendor App JavaScript -->
    <script>
        function vendorApp() {
            return {
                mobileSidebarOpen: false,
                sidebarMinimized: false,

                initApp() {
                    // Initialize app
                    this.handleResize();
                    // Load sidebar state from localStorage
                    this.sidebarMinimized = localStorage.getItem('vendor-sidebar-minimized') === 'true';
                },

                toggleMobileSidebar() {
                    this.mobileSidebarOpen = !this.mobileSidebarOpen;
                },

                toggleSidebar() {
                    this.sidebarMinimized = !this.sidebarMinimized;
                    // Save state to localStorage
                    localStorage.setItem('vendor-sidebar-minimized', this.sidebarMinimized);
                },

                closeMobileSidebar() {
                    this.mobileSidebarOpen = false;
                },

                closeDropdowns() {
                    // Close any open dropdowns
                },

                handleResize() {
                    if (window.innerWidth >= 1024) {
                        this.mobileSidebarOpen = false;
                    }
                }
            }
        }
    </script>

</body>
</html>